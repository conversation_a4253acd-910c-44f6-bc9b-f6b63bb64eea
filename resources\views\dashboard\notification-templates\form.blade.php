@push('css')
<style>
    #languageTabs {
        border-bottom: 2px solid #f0f0f0;
        margin-bottom: 0;
    }

    #languageTabs li {
        margin-bottom: -2px;
    }

    #languageTabs li a {
        background-color: #f8f9fa;
        color: #6c757d;
        border: 1px solid #dee2e6;
        border-radius: 8px 8px 0 0;
        padding: 12px 24px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    #languageTabs li a:hover {
        background-color: #e9ecef;
        color: #495057;
        text-decoration: none;
    }

    #languageTabs li.active a,
    #languageTabs li.active a:hover,
    #languageTabs li.active a:focus {
        background-color: #ffce32;
        color: #000;
        border-color: #ffce32;
        border-radius: 8px 8px 0 0;
    }

    .tab-content {
        padding: 20px 0;
    }

    .form_field_padding {
        margin-bottom: 20px;
    }

    .form_field_padding label {
        font-weight: 600;
        margin-bottom: 8px;
        color: #333;
    }

    .form-control {
        border: 1px solid #ddd;
        border-radius: 6px;
        padding: 12px 15px;
        font-size: 14px;
    }

    .form-control:focus {
        border-color: #ffce32;
        box-shadow: 0 0 0 0.2rem rgba(255, 206, 50, 0.25);
    }

    .btn.create_btn {
        background-color: #ffce32;
        border: none;
        color: #000;
        padding: 12px 30px;
        border-radius: 6px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn.create_btn:hover {
        background-color: #e6b82e;
        transform: translateY(-1px);
    }

    .help-block {
        color: #dc3545;
        font-size: 12px;
        margin-top: 5px;
    }

    .has-error .form-control {
        border-color: #dc3545;
    }

    .notification-key-field {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        padding: 15px;
        border-radius: 6px;
        margin-bottom: 20px;
    }

    .available-placeholders {
        background-color: #e7f3ff;
        border: 1px solid #b3d9ff;
        padding: 15px;
        border-radius: 6px;
        margin-top: 10px;
    }

    .placeholder-tag {
        background-color: #007bff;
        color: white;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 11px;
        margin: 2px;
        display: inline-block;
    }

    /* Taggable Input Styles */
    .tags-input-container {
        border: 1px solid #ddd;
        border-radius: 6px;
        padding: 8px;
        min-height: 50px;
        background-color: #fff;
        cursor: text;
        display: flex;
        flex-wrap: wrap;
        align-items: flex-start;
        gap: 5px;
    }

    .tags-input-container:focus-within {
        border-color: #ffce32;
        box-shadow: 0 0 0 0.2rem rgba(255, 206, 50, 0.25);
    }

    .tags-input-container.has-error {
        border-color: #dc3545;
    }

    .tag-item {
        background-color: #ffce32;
        color: #000;
        padding: 4px 8px;
        border-radius: 15px;
        font-size: 12px;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
        gap: 5px;
        margin: 2px 0;
        animation: tagFadeIn 0.2s ease-in;
    }

    .tag-item .tag-remove {
        cursor: pointer;
        font-weight: bold;
        font-size: 14px;
        line-height: 1;
        opacity: 0.7;
        transition: opacity 0.2s;
    }

    .tag-item .tag-remove:hover {
        opacity: 1;
    }

    .tag-input {
        border: none;
        outline: none;
        background: transparent;
        flex: 1;
        min-width: 120px;
        padding: 4px;
        font-size: 14px;
    }

    .tag-input::placeholder {
        color: #999;
    }

    @keyframes tagFadeIn {
        from {
            opacity: 0;
            transform: scale(0.8);
        }
        to {
            opacity: 1;
            transform: scale(1);
        }
    }

    .placeholders-help {
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        padding: 12px;
        margin-top: 10px;
    }

    .placeholders-help h6 {
        margin: 0 0 8px 0;
        color: #495057;
        font-weight: 600;
    }

    .suggested-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
    }

    .suggested-tag {
        background-color: #e9ecef;
        color: #495057;
        padding: 3px 8px;
        border-radius: 12px;
        font-size: 11px;
        cursor: pointer;
        transition: all 0.2s;
        border: 1px solid transparent;
    }

    .suggested-tag:hover {
        background-color: #ffce32;
        color: #000;
        border-color: #ffce32;
    }
</style>
@endpush

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="white_card card_height_100 mb_30">
                <div class="white_card_header">
                    <div class="box_header m-0">
                        <div class="main-title">
                            <h3 class="m-0">{{ isset($template) ? 'Edit' : 'Create' }} Notification Template</h3>
                        </div>
                        <div class="header_more_tool">
                            <a href="{{ route('notification-templates.index') }}" class="btn btn-secondary">
                                <i class="fa fa-arrow-left"></i> Back
                            </a>
                        </div>
                    </div>
                </div>
                <div class="white_card_body">
                    <form action="{{ isset($template) ? route('notification-templates.update', $template->id) : route('notification-templates.store') }}" 
                          method="POST">
                        @csrf
                        @if(isset($template))
                            @method('PUT')
                        @endif

                        <div id="inputFormRow">
                            <!-- Notification Key Field -->
                            <div class="notification-key-field">
                                <div class="form_field_padding {{ $errors->has('key') ? 'has-error' : '' }}">
                                    <label for="key">Notification Key *</label>
                                    <input type="text" 
                                           class="form-control" 
                                           name="key" 
                                           id="key"
                                           value="{{ old('key', $template->key ?? '') }}" 
                                           placeholder="e.g., booking_placed, user_registered"
                                           {{ isset($template) && $template->type === 'system' ? 'readonly' : '' }}>
                                    {!! $errors->first('key', '<p class="help-block">:message</p>') !!}
                                    <small class="text-muted">
                                        This key will be used in your code to reference this notification template.
                                    </small>
                                </div>

                                @if(!isset($template))
                                <div class="form_field_padding">
                                    <label for="type">Template Type</label>
                                    <select class="form-control" name="type" id="type">
                                        <option value="custom">Custom</option>
                                        <option value="system">System</option>
                                    </select>
                                </div>
                                @endif
                            </div>

                            <!-- Placeholders Field -->
                            <div class="form_field_padding">
                                <label for="placeholders">Available Placeholders</label>
                                <div class="tags-input-container {{ $errors->has('placeholders_input') ? 'has-error' : '' }}" id="tagsContainer">
                                    <input type="text"
                                           class="tag-input"
                                           id="tagInput"
                                           placeholder="Type placeholder name and press Enter..."
                                           autocomplete="off">
                                </div>

                                <!-- Hidden input to store the actual data -->
                                <input type="hidden"
                                       name="placeholders_input"
                                       id="placeholders_input"
                                       value="{{ isset($template) ? implode(', ', $template->getPlaceholdersList()) : '' }}">

                                {!! $errors->first('placeholders_input', '<p class="help-block">:message</p>') !!}

                                <div class="placeholders-help">
                                    <h6><i class="fa fa-lightbulb-o"></i> Common Placeholders:</h6>
                                    <div class="suggested-tags">
                                        <span class="suggested-tag" data-tag="customer_name">customer_name</span>
                                        <span class="suggested-tag" data-tag="listing_name">listing_name</span>
                                        <span class="suggested-tag" data-tag="check_in_date">check_in_date</span>
                                        <span class="suggested-tag" data-tag="check_out_date">check_out_date</span>
                                        <span class="suggested-tag" data-tag="booking_id">booking_id</span>
                                        <span class="suggested-tag" data-tag="total_amount">total_amount</span>
                                        <span class="suggested-tag" data-tag="host_name">host_name</span>
                                        <span class="suggested-tag" data-tag="guest_count">guest_count</span>
                                    </div>
                                    <small class="text-muted">
                                        <i class="fa fa-info-circle"></i> Click on suggested tags or type custom placeholder names. Press Enter to add tags.
                                    </small>
                                </div>
                            </div>

                            <!-- Language Tabs -->
                            <ul class="nav nav-tabs" id="languageTabs" role="tablist">
                                <li role="presentation" class="active">
                                    <a href="#en" aria-controls="en" role="tab" data-toggle="tab">English</a>
                                </li>
                                <li role="presentation">
                                    <a href="#es" aria-controls="es" role="tab" data-toggle="tab">Español</a>
                                </li>
                            </ul>

                            <!-- Tab Content -->
                            <div class="tab-content" id="languageTabContent">
                                <!-- English Tab -->
                                <div role="tabpanel" class="tab-pane active" id="en">
                                    <div class="form_field_padding {{ $errors->has('translations.en.title') ? 'has-error' : '' }}">
                                        <label for="title_en">Title (English) *</label>
                                        <input type="text" 
                                               class="form-control" 
                                               name="translations[en][title]" 
                                               id="title_en"
                                               value="{{ old('translations.en.title', isset($template) ? $template->getTranslation('en', 'title') : '') }}"
                                               placeholder="E.g., Booking Confirmation">
                                        {!! $errors->first('translations.en.title', '<p class="help-block">:message</p>') !!}
                                    </div>
                                    <div class="form_field_padding {{ $errors->has('translations.en.message') ? 'has-error' : '' }}">
                                        <label for="message_en">Message (English) *</label>
                                        <textarea class="form-control" 
                                                  name="translations[en][message]" 
                                                  id="message_en"
                                                  rows="4"
                                                  placeholder="Your booking has been confirmed...">{{ old('translations.en.message', isset($template) ? $template->getTranslation('en', 'message') : '') }}</textarea>
                                        {!! $errors->first('translations.en.message', '<p class="help-block">:message</p>') !!}
                                        
                                        <div class="available-placeholders">
                                            <strong>Available Placeholders:</strong><br>
                                            @if(isset($template) && $template->placeholders)
                                                @foreach($template->getPlaceholdersList() as $placeholder)
                                                    <span class="placeholder-tag">:{{ $placeholder }}</span>
                                                @endforeach
                                            @else
                                                <span class="placeholder-tag">:customer_name</span>
                                                <span class="placeholder-tag">:listing_name</span>
                                                <!-- default placeholders -->
                                            @endif
                                        </div>
                                    </div>
                                </div>

                                <!-- Spanish Tab -->
                                <div role="tabpanel" class="tab-pane" id="es">
                                    <div class="form_field_padding {{ $errors->has('translations.es.title') ? 'has-error' : '' }}">
                                        <label for="title_es">Title (Español) *</label>
                                        <input type="text" 
                                               class="form-control" 
                                               name="translations[es][title]" 
                                               id="title_es"
                                               value="{{ old('translations.es.title',isset($template) ? $template->getTranslation('es', 'title') : '') }}"
                                               placeholder="E.g., Confirmación de Reserva">
                                        {!! $errors->first('translations.es.title', '<p class="help-block">:message</p>') !!}
                                    </div>
                                    <div class="form_field_padding {{ $errors->has('translations.es.message') ? 'has-error' : '' }}">
                                        <label for="message_es">Message (Español) *</label>
                                        <textarea class="form-control" 
                                                  name="translations[es][message]" 
                                                  id="message_es"
                                                  rows="4"
                                                  placeholder="Su reserva ha sido confirmada...">{{ old('translations.es.message',isset($template) ? $template->getTranslation('es', 'message') : '') }}</textarea>
                                        {!! $errors->first('translations.es.message', '<p class="help-block">:message</p>') !!}
                                        
                                        <div class="available-placeholders">
                                            <strong>Marcadores Disponibles:</strong><br>
                                            @if(isset($template) && $template->placeholders)
                                                @foreach($template->getPlaceholdersList() as $placeholder)
                                                    <span class="placeholder-tag">:{{ $placeholder }}</span>
                                                @endforeach
                                            @else
                                                <span class="placeholder-tag">:customer_name</span>
                                                <span class="placeholder-tag">:listing_name</span>
                                                <!-- default placeholders -->
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="form_field_padding">
                                <div class="list_form_btn">
                                    <input class="btn create_btn" type="submit" value="{{ isset($template) ? 'Update Template' : 'Create Template' }}">
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@push('js')
<script>
$(document).ready(function() {
    let tags = [];
    const tagInput = $('#tagInput');
    const tagsContainer = $('#tagsContainer');
    const hiddenInput = $('#placeholders_input');

    // Initialize with existing tags
    const existingTags = hiddenInput.val();
    if (existingTags) {
        tags = existingTags.split(',').map(tag => tag.trim()).filter(tag => tag);
        renderTags();
    }

    // Add tag function
    function addTag(tagText) {
        tagText = tagText.trim().toLowerCase().replace(/[^a-z0-9_]/g, '');

        if (tagText && !tags.includes(tagText)) {
            tags.push(tagText);
            renderTags();
            updateHiddenInput();
            tagInput.val('');
        }
    }

    // Remove tag function
    function removeTag(tagToRemove) {
        tags = tags.filter(tag => tag !== tagToRemove);
        renderTags();
        updateHiddenInput();
    }

    // Render tags in the container
    function renderTags() {
        // Remove existing tag items
        tagsContainer.find('.tag-item').remove();

        // Add tag items before the input
        tags.forEach(tag => {
            const tagElement = $(`
                <div class="tag-item">
                    <span>${tag}</span>
                    <span class="tag-remove" data-tag="${tag}">&times;</span>
                </div>
            `);
            tagElement.insertBefore(tagInput);
        });
    }

    // Update hidden input value
    function updateHiddenInput() {
        hiddenInput.val(tags.join(', '));
        updatePlaceholderDisplay();
    }

    // Update placeholder display in both language tabs
    function updatePlaceholderDisplay() {
        const placeholderHtml = tags.map(tag => `<span class="placeholder-tag">:${tag}</span>`).join(' ');
        $('.available-placeholders').each(function() {
            const strongText = $(this).find('strong').text();
            $(this).html(`<strong>${strongText}</strong><br>${placeholderHtml || '<span class="text-muted">No placeholders added yet</span>'}`);
        });
    }

    // Handle Enter key and comma
    tagInput.on('keydown', function(e) {
        if (e.key === 'Enter' || e.key === ',') {
            e.preventDefault();
            addTag($(this).val());
        } else if (e.key === 'Backspace' && $(this).val() === '' && tags.length > 0) {
            // Remove last tag when backspace is pressed on empty input
            removeTag(tags[tags.length - 1]);
        }
    });

    // Handle tag removal
    tagsContainer.on('click', '.tag-remove', function() {
        const tagToRemove = $(this).data('tag');
        removeTag(tagToRemove);
    });

    // Handle suggested tag clicks
    $('.suggested-tag').on('click', function() {
        const tagText = $(this).data('tag');
        addTag(tagText);
        $(this).addClass('btn-success').text('Added!');
        setTimeout(() => {
            $(this).removeClass('btn-success').text(tagText);
        }, 1000);
    });

    // Focus input when container is clicked
    tagsContainer.on('click', function() {
        tagInput.focus();
    });

    // Handle paste events
    tagInput.on('paste', function(e) {
        setTimeout(() => {
            const pastedText = $(this).val();
            const pastedTags = pastedText.split(/[,\s]+/).filter(tag => tag.trim());

            $(this).val('');
            pastedTags.forEach(tag => addTag(tag));
        }, 10);
    });

    // Initialize placeholder display
    updatePlaceholderDisplay();

    // Form validation
    $('form').on('submit', function(e) {
        if (tags.length === 0) {
            e.preventDefault();
            alert('Please add at least one placeholder.');
            tagInput.focus();
            return false;
        }
    });
});
</script>
@endpush
